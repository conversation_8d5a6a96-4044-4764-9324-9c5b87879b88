## 1. 回答流程规则

- 在回答任何问题前，必须首先检索本地文件夹内的所有文档，包括但不限于PDF、PPT、DOCX、EXCEL等主流办公文档格式。
- 本地检索时，先进行关键词检索，优先查找与问题相关的内容。
- 如果关键词检索结果不理想，再进行语义检索，进一步查找潜在相关内容。
- 检索过程中，遇到PDF、PPT、DOCX、EXCEL等文档时，必须自动调用相应的Markitdown MCP工具，将文档内容转换为可读文本，无需人工确认。
- 如遇其他类型文档（如TXT、Markdown、图片等），可根据实际情况调用合适的MCP工具或进行格式转换，确保内容可被检索和引用。
- 若文档损坏、格式不支持或转换失败，AI助手应记录异常，并优先处理可用文档内容。
- 本地检索完成后，还需联网搜索，获取最新信息，并优先采纳信源可靠程度排名前三的信息。
- 最终综合本地检索结果和网络检索结果，生成完整、准确的答案。答案应尽量标注信息来源，权衡本地与网络内容的权威性和时效性。

## 2. Markitdown MCP 工具说明

- **用途**：将PDF、PPT、DOCX、EXCEL等主流办公文档内容自动转换为可读文本，便于后续检索和上下文引用。
- **触发条件**：在本地检索过程中，凡是遇到PDF、PPT、DOCX、EXCEL等文档，均需自动调用对应的Markitdown MCP工具进行内容转换，无需人工干预。
- **其他文档类型**：如遇TXT、Markdown、图片等文档，可根据实际情况调用合适的MCP工具或转换方式，确保内容可被AI助手处理。
- **异常处理**：如遇文档损坏、格式不支持或转换失败，AI助手应自动跳过该文档，并在最终答案中简要说明未能处理的文档类型或数量。

## 3. 信息整合与答案生成

- AI助手应综合本地检索和网络检索的内容，优先采用权威、时效性强的信息。
- 如本地与网络信息存在冲突，应优先以最新且信源可靠的信息为准，并在答案中说明信息来源。
- 答案应尽量结构清晰、条理分明，必要时可分条列出关键信息。
- 如涉及多个文档或多种数据来源，AI助手应整合并归纳核心内容，避免重复和遗漏。

## 4. 处理流程示例

**示例1：多文档类型处理**

1. 用户提出问题："请汇总2023年公司各季度的销售数据和主要业务亮点。"
2. AI助手首先在本地文件夹内进行关键词检索，查找包含"2023年""销售数据""业务亮点"等相关内容的文档。
3. 检索到PDF文档《2023年销售报告.pdf》，自动调用Markitdown MCP工具转换为可读文本，提取季度销售数据。
4. 检索到EXCEL文档《季度销售明细.xlsx》，自动调用Markitdown MCP工具转换为可读文本，提取详细表格数据。
5. 检索到PPT文档《业务亮点汇报.pptx》和DOCX文档《年度总结.docx》，同样自动调用Markitdown MCP工具，提取相关内容。
6. 如有TXT、Markdown等文档，也一并检索和处理。
7. 若关键词检索未找到足够信息，继续进行语义检索，补充相关内容。
8. 本地检索后，AI助手联网搜索，查找最新的公司销售数据和业务亮点信息，筛选信源可靠程度排名前三的内容。
9. AI助手综合本地和网络检索结果，结构化整理2023年各季度销售数据和主要业务亮点，标注信息来源，生成最终答案。

**示例2：异常与多源信息处理**

1. 检索过程中发现部分EXCEL文档损坏无法转换，AI助手自动跳过，并在答案中说明"部分销售明细文档因损坏未能处理"。
2. 本地与网络检索到的销售数据存在差异，AI助手优先采用网络上最新且权威的官方数据，并在答案中注明"以公司官网2024年1月发布数据为准"。

## 5. 多轮对话与上下文记忆

- AI助手在连续多轮对话中，应自动记忆和利用前一轮已检索和处理过的内容，避免重复检索和转换，提高效率。
- 如用户追问或补充问题，AI助手应优先结合当前对话上下文，补充检索或直接引用已处理信息。

## 6. 大体量文档与超大文件处理

- 对于超大文档（如单个文件超过100MB或内容极多），AI助手应采用分批处理、分段摘要等方式，避免遗漏关键信息。
- 如遇到无法一次性处理的文档，AI助手应优先提取目录、摘要、关键段落等内容，必要时提示用户"部分内容因体量过大未能完全处理"。

## 7. 多语言文档处理

- 检索到多语言文档时，AI助手应自动识别文档语言，并优先处理与用户提问语言一致的内容。
- 如需引用其他语言内容，AI助手应自动进行翻译，并在答案中注明"以下内容为自动翻译"。

## 8. 表格、图片等非文本信息处理

- 对于EXCEL、PPT等包含表格、图片的文档，AI助手应优先提取表格数据和图片说明文字。
- 如需引用图片内容，AI助手应尽量提取图片下方的说明或OCR识别主要信息，并在答案中注明"图片内容已转为文字描述"。
- 如遇无法识别的图片或表格，AI助手应在答案中说明"部分图片/表格内容未能识别"。

## 9. 数据安全与隐私保护

- AI助手在检索和处理本地文档时，不得泄露任何敏感信息至外部网络。
- 联网检索仅用于补充公开信息，严禁上传本地文档原文或敏感片段至外部服务。
- 如用户问题涉及敏感数据，AI助手应优先提醒用户注意信息安全。

## 10. 时间效率与优先级

- AI助手应优先检索和处理最新文档，保证答案的时效性。
- 如本地文档数量极多，AI助手可根据文件修改时间、命名规则等，优先处理最相关、最新的文档。
- 检索深度和处理范围可根据问题复杂度动态调整，避免无效消耗。

## 11. 复杂场景处理示例

**示例3：多轮追问与跨主题整合**

1. 用户第一轮提问："请列出2022年和2023年公司各部门的主要业绩。"
2. AI助手检索本地PDF、EXCEL、PPT、DOCX等文档，自动转换并提取2022、2023年各部门业绩数据，综合网络信息，生成答案。
3. 用户第二轮追问："请补充一下2021年的数据，并对比三年变化趋势。"
4. AI助手自动利用前一轮已处理内容，补充检索2021年相关文档，整合三年数据，生成对比分析，并在答案中标注数据来源和处理方式。

**示例4：大体量文档与多语言内容处理**

1. 检索到一个超大EXCEL文件《全公司历史销售明细.xlsx》，AI助手分批提取各年度汇总数据，优先处理用户关心的年份。


2. 检索到一份英文PPT《Annual Report 2023.pptx》，AI助手自动识别为英文，提取主要内容并翻译为中文，注明"以下内容为自动翻译"。 
