import sys
import os

def read_docx(file_path):
    """读取 .docx 文件内容"""
    try:
        import docx
    except ImportError:
        print("错误：未安装 python-docx 库，请运行 'pip install python-docx'", file=sys.stderr)
        sys.exit(1)

    try:
        document = docx.Document(file_path)
        return '\n'.join([para.text for para in document.paragraphs])
    except Exception as e:
        print(f"读取 {file_path} 时出错: {e}", file=sys.stderr)
        sys.exit(1)

def read_pptx(file_path):
    """读取 .pptx 文件内容"""
    try:
        import pptx
    except ImportError:
        print("错误：未安装 python-pptx 库，请运行 'pip install python-pptx'", file=sys.stderr)
        sys.exit(1)

    try:
        presentation = pptx.Presentation(file_path)
        full_text = []
        for slide in presentation.slides:
            for shape in slide.shapes:
                if not shape.has_text_frame:
                    continue
                for paragraph in shape.text_frame.paragraphs:
                    for run in paragraph.runs:
                        full_text.append(run.text)
        return '\n'.join(full_text)
    except Exception as e:
        print(f"读取 {file_path} 时出错: {e}", file=sys.stderr)
        sys.exit(1)

def main():
    if len(sys.argv) < 2:
        print("用法: python gemini_office_reader.py <文件路径>", file=sys.stderr)
        sys.exit(1)

    file_path = sys.argv[1]
    if file_path.startswith('"') and file_path.endswith('"'):
        file_path = file_path[1:-1]

    if not os.path.exists(file_path):
        print(f"错误：未找到文件 {file_path}", file=sys.stderr)
        sys.exit(1)

    file_ext = os.path.splitext(file_path)[1].lower()

    if file_ext == '.docx':
        content = read_docx(file_path)
    elif file_ext == '.pptx':
        content = read_pptx(file_path)
    else:
        print(f"错误：不支持的文件类型 '{file_ext}'，仅支持 .docx 和 .pptx", file=sys.stderr)
        sys.exit(1)

    print(content)

if __name__ == "__main__":
    main()

