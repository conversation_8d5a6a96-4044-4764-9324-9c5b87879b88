import csv
import os
from datetime import datetime

def create_schedule(filename=None):
    """创建时间表CSV文件

    Args:
        filename (str, optional): 输出文件名，默认为带时间戳的文件名

    Returns:
        str: 生成的文件路径，如果失败返回None
    """

    schedule_data = [
        ["时间段", "活动", "时长", "类型", "备注"],
        ["07:00-07:30", "英文单词学习", "30分钟", "英语学习", "记忆力最佳时段"],
        ["07:30-08:00", "英文语法学习", "30分钟", "英语学习", "逻辑思维清晰"],
        ["09:00-09:30", "AI课程学习", "30分钟", "专业学习", "学习新概念最佳时段"],
        ["09:30-10:00", "课程练习", "30分钟", "专业学习", "巩固理论知识"],
        ["10:30-11:00", "英文听力学习", "30分钟", "英语学习", "听觉注意力较好"],
        ["14:00-14:30", "复习", "30分钟", "复习巩固", "巩固上午学习内容"],
        ["15:00-16:00", "辅导学生", "60分钟", "教学工作", "思维活跃，适合互动"],
        ["17:00-18:00", "读书", "60分钟", "阅读思考", "深度思考时段"],
        ["19:00-21:00", "锻炼", "120分钟", "体能训练", "释放压力，增强体质"]
    ]

    # 如果没有指定文件名，使用带时间戳的默认文件名
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"daily_schedule_{timestamp}.csv"

    # 确保文件名以.csv结尾
    if not filename.endswith('.csv'):
        filename += '.csv'

    # 获取完整路径
    filepath = os.path.abspath(filename)

    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        with open(filepath, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerows(schedule_data)

        print(f"✅ 时间表已成功生成：{filepath}")
        print(f"📁 文件大小：{os.path.getsize(filepath)} 字节")
        print("📊 学习统计：")
        print("  • 英语学习：1.5小时")
        print("  • 专业学习：1小时")
        print("  • 复习巩固：0.5小时")
        print("  • 教学工作：1小时")
        print("  • 阅读思考：1小时")
        print("  • 体能训练：2小时")
        print("  • 总计：7小时")

        return filepath

    except PermissionError:
        print(f"❌ 权限错误：无法写入文件 {filepath}")
        print("💡 建议：请检查文件权限或选择其他目录")
        return None
    except OSError as e:
        print(f"❌ 系统错误：{e}")
        print("💡 建议：请检查磁盘空间和文件路径")
        return None
    except Exception as e:
        print(f"❌ 创建文件失败：{e}")
        return None

if __name__ == "__main__":
    create_schedule()